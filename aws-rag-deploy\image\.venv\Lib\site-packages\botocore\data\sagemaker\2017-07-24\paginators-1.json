{"pagination": {"ListTrainingJobs": {"result_key": "TrainingJobSummaries", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListEndpoints": {"result_key": "Endpoints", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListEndpointConfigs": {"result_key": "EndpointConfigs", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListNotebookInstances": {"result_key": "NotebookInstances", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListTags": {"result_key": "Tags", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListModels": {"result_key": "Models", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListAlgorithms": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AlgorithmSummaryList"}, "ListCodeRepositories": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "CodeRepositorySummaryList"}, "ListCompilationJobs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "CompilationJobSummaries"}, "ListHyperParameterTuningJobs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "HyperParameterTuningJobSummaries"}, "ListLabelingJobs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "LabelingJobSummaryList"}, "ListLabelingJobsForWorkteam": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "LabelingJobSummaryList"}, "ListModelPackages": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ModelPackageSummaryList"}, "ListNotebookInstanceLifecycleConfigs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "NotebookInstanceLifecycleConfigs"}, "ListSubscribedWorkteams": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "SubscribedWorkteams"}, "ListTrainingJobsForHyperParameterTuningJob": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "TrainingJobSummaries"}, "ListTransformJobs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "TransformJobSummaries"}, "ListWorkteams": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Workteams"}, "Search": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Results"}, "ListApps": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Apps"}, "ListAutoMLJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AutoMLJobSummaries"}, "ListCandidatesForAutoMLJob": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Candidates"}, "ListDomains": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Domains"}, "ListExperiments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ExperimentSummaries"}, "ListFlowDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FlowDefinitionSummaries"}, "ListHumanTaskUis": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "HumanTaskUiSummaries"}, "ListMonitoringExecutions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "MonitoringExecutionSummaries"}, "ListMonitoringSchedules": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "MonitoringScheduleSummaries"}, "ListProcessingJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ProcessingJobSummaries"}, "ListTrialComponents": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TrialComponentSummaries"}, "ListTrials": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TrialSummaries"}, "ListUserProfiles": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "UserProfiles"}, "ListWorkforces": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Workforces"}, "ListImageVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ImageVersions"}, "ListImages": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Images"}, "ListActions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ActionSummaries"}, "ListAppImageConfigs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AppImageConfigs"}, "ListArtifacts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ArtifactSummaries"}, "ListAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AssociationSummaries"}, "ListContexts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ContextSummaries"}, "ListFeatureGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FeatureGroupSummaries"}, "ListModelPackageGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ModelPackageGroupSummaryList"}, "ListPipelineExecutionSteps": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "PipelineExecutionSteps"}, "ListPipelineExecutions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "PipelineExecutionSummaries"}, "ListPipelineParametersForExecution": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "PipelineParameters"}, "ListPipelines": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "PipelineSummaries"}, "ListDataQualityJobDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "JobDefinitionSummaries"}, "ListDeviceFleets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DeviceFleetSummaries"}, "ListDevices": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DeviceSummaries"}, "ListEdgePackagingJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "EdgePackagingJobSummaries"}, "ListModelBiasJobDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "JobDefinitionSummaries"}, "ListModelExplainabilityJobDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "JobDefinitionSummaries"}, "ListModelQualityJobDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "JobDefinitionSummaries"}, "ListStudioLifecycleConfigs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "StudioLifecycleConfigs"}, "ListInferenceRecommendationsJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "InferenceRecommendationsJobs"}, "ListLineageGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "LineageGroupSummaries"}, "ListModelMetadata": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ModelMetadataSummaries"}, "ListEdgeDeploymentPlans": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "EdgeDeploymentPlanSummaries"}, "ListStageDevices": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DeviceDeploymentSummaries"}, "ListInferenceRecommendationsJobSteps": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Steps"}, "ListInferenceExperiments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "InferenceExperiments"}, "ListModelCardExportJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ModelCardExportJobSummaries"}, "ListModelCardVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ModelCardVersionSummaryList"}, "ListModelCards": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ModelCardSummaries"}, "ListMonitoringAlertHistory": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "MonitoringAlertHistory"}, "ListMonitoringAlerts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "MonitoringAlertSummaries"}, "ListSpaces": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Spaces"}, "ListAliases": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "SageMakerImageVersionAliases"}, "ListResourceCatalogs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ResourceCatalogs"}, "ListClusterNodes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ClusterNodeSummaries"}, "ListClusters": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ClusterSummaries"}, "ListInferenceComponents": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "InferenceComponents"}, "ListMlflowTrackingServers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TrackingServerSummaries"}, "ListOptimizationJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "OptimizationJobSummaries"}, "ListClusterSchedulerConfigs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ClusterSchedulerConfigSummaries"}, "ListComputeQuotas": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ComputeQuotaSummaries"}, "ListPartnerApps": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Summaries"}, "ListTrainingPlans": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TrainingPlanSummaries"}}}