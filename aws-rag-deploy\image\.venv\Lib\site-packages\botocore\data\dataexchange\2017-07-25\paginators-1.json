{"pagination": {"ListDataSetRevisions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Revisions"}, "ListDataSets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DataSets"}, "ListJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Jobs"}, "ListRevisionAssets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Assets"}, "ListEventActions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "EventActions"}, "ListDataGrants": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DataGrantSummaries"}, "ListReceivedDataGrants": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DataGrantSummaries"}}}