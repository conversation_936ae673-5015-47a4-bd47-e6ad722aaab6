{"pagination": {"ListDeploymentJobs": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "deploymentJobs"}, "ListFleets": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "fleetDetails"}, "ListRobotApplications": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "robotApplicationSummaries"}, "ListRobots": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "robots"}, "ListSimulationApplications": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "simulationApplicationSummaries"}, "ListSimulationJobs": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "simulationJobSummaries"}, "ListSimulationJobBatches": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "simulationJobBatchSummaries"}, "ListWorldExportJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "worldExportJobSummaries"}, "ListWorldGenerationJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "worldGenerationJobSummaries"}, "ListWorldTemplates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "templateSummaries"}, "ListWorlds": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "worldSummaries"}}}